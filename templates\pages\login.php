<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

// คอนฟิกการล็อกอิน
define('BAM_MAX_LOGIN_ATTEMPTS', 3);
define('BAM_LOCKOUT_TIME', 300); // 5 นาที

/**
 * ดึงค่า session keys
 */
function bam_get_session_keys()
{
    return array(
        'user_id' => 'bam_user_id',
        'username' => 'bam_username',
        'role_id' => 'bam_role_id',
        'attempts' => 'login_attempts',
        'last_attempt' => 'last_attempt_time'
    );
}

/**
 * แสดงฟอร์มล็อกอิน
 */
function bam_render_login_form()
{
    ob_start();
    ?>
    <div class="container-fluid min-vh-100 d-flex justify-content-center align-items-center bg-body-tertiary">
        <div class="row bg-white rounded-4 shadow w-100 overflow-hidden" style="max-width: 900px;">
            <?php echo bam_render_login_column(); ?>
            <?php echo bam_render_notification_column(); ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * แสดงคอลัมน์ฟอร์มล็อกอิน
 */
function bam_render_login_column()
{
    ob_start();
    ?>
    <div class="col-md-6 pt-4 pb-3 px-4">
        <?php echo bam_render_brand_header(); ?>
        <?php echo bam_render_login_title(); ?>
        <?php echo bam_render_login_form_fields(); ?>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * แสดงส่วนหัวแบรนด์
 */
function bam_render_brand_header()
{
    return '
    <div class="d-flex align-items-center justify-content-start pb-3 text-nowrap border-bottom">
        <div class="brand-link d-flex align-items-center text-decoration-none">
            <img src="https://yt3.ggpht.com/yti/ANjgQV8vZutCTvT9vmnGFYpOcP7-p_jw7lGaLC-koLXb3UiNnA=s88-c-k-c0x00ffffff-no-rj"
                 alt="AdminLTE Logo"
                 class="brand-image-login rounded-circle shadow float-start border border-primary">
            <span class="c-brand-text fw-bold text-black ms-2 d-md-inline fs-7">BAM SYSTEM</span>
        </div>
    </div>';
}

/**
 * แสดงหัวข้อล็อกอิน
 */
function bam_render_login_title()
{
    return '
    <div class="my-4">
        <h3 class="fw-bold">เข้าสู่ระบบ</h3>
        <span class="text-gray-500">Bank Account Management System</span>
    </div>';
}

/**
 * แสดงฟิลด์ฟอร์มล็อกอิน
 */
function bam_render_login_form_fields()
{
    ob_start();
    ?>
    <form id="bam-login-form" method="POST" action="<?php echo admin_url('admin-post.php'); ?>">
        <input type="hidden" name="action" value="bam_user_login">
        <?php wp_nonce_field('bam_login_action', 'bam_login_nonce'); ?>

        <label for="username" class="form-label">ชื่อผู้ใช้</label>
        <input type="text" name="username" class="form-control" placeholder="ชื่อผู้ใช้" required><br>

        <label for="password" class="form-label">รหัสผ่าน</label>
        <input type="password" name="password" class="form-control" placeholder="รหัสผ่าน" required><br>

        <div class="error-alert"><!-- alert จะแสดงตรงนี้ --></div>

        <div class="mt-4">
            <button type="submit" class="btn btn-primary w-100">เข้าสู่ระบบ</button>
        </div>

        <div class="version-line-wrapper text-center small text-gray-500 mt-4">
            <span class="line"></span><?= get_bam_version() ?><span class="line"></span>
        </div>
    </form>
    <?php
    return ob_get_clean();
}

/**
 * แสดงคอลัมน์แจ้งเตือน
 */
function bam_render_notification_column()
{
    return '
    <div class="col-md-6 p-0 position-relative">
        <img src="https://bam-sys.com/wp-content/uploads/2025/06/img-login-1.png"
             alt="เข้าสู่ระบบ BAM"
             class="img-fluid w-100 h-100 object-fit-cover position-absolute top-0 start-0" />

        <div class="position-relative text-white p-4 z-1">
            <h3 class="fw-bold text-shadow">แจ้งเตือน</h3>
            <div class="bg-blur p-3 rounded-3 text-black">
                <div class="position-absolute top-0 end-0 me-2 mt-1 small text-muted">
                    <p>26/05/2025</p>
                </div>
                <div class="row align-items-center">
                    <div class="col-md-1 col-1 me-2 fs-3">
                        <i class="bi bi-key-fill"></i>
                    </div>
                    <div class="col-md-10 col-9">
                        <span><strong>แจ้งเตือนความปลอดภัย</strong><br>ไม่ควรบันทึกรหัสผ่านผู้ใช้งานของท่านไว้บนบราวเซอร์ทุกกรณี</span>
                    </div>
                </div>
            </div>
        </div>
    </div>';
}

add_shortcode('bam_login_form', 'bam_render_login_form');

/**
 * จัดการการล็อกอิน
 */
function bam_handle_login()
{
    require_once get_template_directory() . '/includes/permission-handler.php';

    header('Content-Type: application/json');
    session_start();

    try {
        // ตรวจสอบ CSRF token
        bam_validate_nonce();

        // ตรวจสอบการบล็อกบัญชี
        bam_check_account_lockout();

        // ตรวจสอบข้อมูลล็อกอิน
        $credentials = bam_sanitize_credentials();
        $user = bam_authenticate_user($credentials);

        if ($user) {
            bam_handle_successful_login($user);
        } else {
            bam_handle_failed_login();
        }

    } catch (Exception $e) {
        bam_send_error_response($e->getMessage());
    }
}

/**
 * ตรวจสอบ CSRF token
 */
function bam_validate_nonce()
{
    if (!isset($_POST['bam_login_nonce']) || !wp_verify_nonce($_POST['bam_login_nonce'], 'bam_login_action')) {
        throw new Exception('ไม่สามารถยืนยันความถูกต้องของแบบฟอร์มได้');
    }
}

/**
 * ตรวจสอบการบล็อกบัญชี
 */
function bam_check_account_lockout()
{
    $session_keys = bam_get_session_keys();
    $attempts = isset($_SESSION[$session_keys['attempts']]) ? $_SESSION[$session_keys['attempts']] : 0;
    $lastAttempt = isset($_SESSION[$session_keys['last_attempt']]) ? $_SESSION[$session_keys['last_attempt']] : 0;

    if ($attempts >= BAM_MAX_LOGIN_ATTEMPTS) {
        $timeSinceLast = time() - $lastAttempt;

        if ($timeSinceLast < BAM_LOCKOUT_TIME) {
            $remaining = BAM_LOCKOUT_TIME - $timeSinceLast;
            throw new Exception("กรุณาลองใหม่อีกครั้งในอีก $remaining วินาที");
        } else {
            // รีเซ็ตถ้าครบเวลาแล้ว
            bam_reset_login_attempts();
        }
    }
}

/**
 * ทำความสะอาดข้อมูลที่ส่งมา
 */
function bam_sanitize_credentials()
{
    return array(
        'username' => sanitize_text_field(isset($_POST['username']) ? $_POST['username'] : ''),
        'password' => sanitize_text_field(isset($_POST['password']) ? $_POST['password'] : '')
    );
}

/**
 * ตรวจสอบข้อมูลผู้ใช้
 */
function bam_authenticate_user($credentials)
{
    global $wpdb;

    $table = $wpdb->prefix . 'assistance';
    $user = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table WHERE username = %s AND is_active = 1",
            $credentials['username']
        ),
        'ARRAY_A'
    );

    if ($user && password_verify($credentials['password'], $user['password_hash'])) {
        return $user;
    }

    return null;
}

/**
 * จัดการเมื่อล็อกอินสำเร็จ
 */
function bam_handle_successful_login($user)
{
    global $wpdb;

    $session_keys = bam_get_session_keys();

    // ตั้งค่า session
    $_SESSION[$session_keys['user_id']] = $user['id'];
    $_SESSION[$session_keys['username']] = $user['username'];
    $_SESSION[$session_keys['role_id']] = $user['role_id'];

    // ตั้งค่าสิทธิ์
    set_user_permission_to_session($user['role_id']);

    // รีเซ็ตการนับครั้งล็อกอิน
    bam_reset_login_attempts();

    // อัปเดตเวลาล็อกอินล่าสุด
    $table = $wpdb->prefix . 'assistance';
    $wpdb->update(
        $table,
        array('last_login' => current_time('mysql')),
        array('id' => $user['id'])
    );

    wp_send_json_success(array('redirect' => home_url('/dashboard')));
}

/**
 * จัดการเมื่อล็อกอินไม่สำเร็จ
 */
function bam_handle_failed_login()
{
    $session_keys = bam_get_session_keys();

    // เพิ่มจำนวนครั้งที่ล็อกอินผิด
    $current_attempts = isset($_SESSION[$session_keys['attempts']]) ? $_SESSION[$session_keys['attempts']] : 0;
    $_SESSION[$session_keys['attempts']] = $current_attempts + 1;
    $_SESSION[$session_keys['last_attempt']] = time();

    throw new Exception('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
}

/**
 * รีเซ็ตการนับครั้งล็อกอิน
 */
function bam_reset_login_attempts()
{
    $session_keys = bam_get_session_keys();
    $_SESSION[$session_keys['attempts']] = 0;
    $_SESSION[$session_keys['last_attempt']] = 0;
}

/**
 * ส่งข้อความผิดพลาด
 */
function bam_send_error_response($message)
{
    echo json_encode(array('success' => false, 'message' => $message));
    exit;
}

/**
 * ฟังก์ชันสำหรับจัดการ login handler
 */
function bam_user_login_handler()
{
    bam_handle_login();
}
add_action('admin_post_nopriv_bam_user_login', 'bam_user_login_handler');
?>